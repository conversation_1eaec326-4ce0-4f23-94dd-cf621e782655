"""
Example sentences to test spaCy and its language models.

>>> from spacy.lang.es.examples import sentences
>>> docs = nlp.pipe(sentences)
"""


sentences = [
    "Apple está buscando comprar una startup del Reino Unido por mil millones de dólares.",
    "Los coches autónomos delegan la responsabilidad del seguro en sus fabricantes.",
    "San Francisco analiza prohibir los robots de reparto.",
    "Londres es una gran ciudad del Reino Unido.",
    "El gato come pescado.",
    "Veo al hombre con el telescopio.",
    "La araña come moscas.",
    "El pingüino incuba en su nido sobre el hielo.",
    "¿Dónde estáis?",
    "¿Quién es el presidente francés?",
    "¿Dónde se encuentra la capital de Argentina?",
    "¿Cuándo nació José de San Martín?",
]
