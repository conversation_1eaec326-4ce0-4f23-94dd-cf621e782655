"""
Example sentences to test spaCy and its language models.

>>> from spacy.lang.lb.examples import sentences
>>> docs = nlp.pipe(sentences)
"""

sentences = [
    "An der Zäit hunn sech den Nordwand an d’Sonn gestridden, wie vun hinnen zwee wuel méi staark wier, wé<PERSON> e <PERSON>, deen an ee waarme Mantel agepak war, iwwert de Wee koum.",
    "Si goufen sech eens, dass deejéinege fir de Stäerkste g<PERSON> sollt, deen de <PERSON> forcéiere géif, säi Mantel auszedoen.",
    "<PERSON> Nordwand huet mat aller Force geblosen, awer wat e méi geblosen huet, wat de Wanderer sech méi a säi Mantel agewéckelt huet.",
    "Um Enn huet den Nordwand säi Kampf opginn.",
    "Dunn huet d’Sonn d’Loft mat hire frëndleche Strale gewiermt, a schonn no kuerzer Zäit huet de Wanderer säi Mantel ausgedoen.",
    "Do huet den Nordwand missen zouginn, dass d’Sonn vun hinnen zwee de Stäerkste wier.",
]
