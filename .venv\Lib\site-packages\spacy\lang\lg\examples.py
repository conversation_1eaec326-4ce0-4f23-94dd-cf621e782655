"""
Example sentences to test spaCy and its language models.

>>> from spacy.lang.lg.examples import sentences
>>> docs = nlp.pipe(sentences)
"""

sentences = [
    "<PERSON>pa ebyafaayo ku byalo <PERSON> ne <PERSON>",
    "<PERSON><PERSON><PERSON> Ttembo kitegeeza kugwa ddalu",
    "<PERSON>kifumu kino kyali kya mulimu ki?",
    "<PERSON>k<PERSON>u we liyise wayitibwa mukululo",
    "Akola mulimu ki oguvaamu ssente?",
    "Emisumaali egikomerera embaawo giyitibwa nninga",
    "Abooluganda ab’emmamba ababiri",
    "Ekisaa<PERSON> ky'ebyenjigiriza kya mugaso nnyo",
]
