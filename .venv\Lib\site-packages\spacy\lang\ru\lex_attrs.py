from ...attrs import LIKE_NUM

_num_words = list(
    set(
        """
ноль ноля нолю нолём ноле нулевой нулевого нулевому нулевым нулевом нулевая нулевую нулевое нулевые нулевых нулевыми 

четверть четверти четвертью четвертей четвертям четвертями четвертях 

треть трети третью третей третям третями третях 

половина половины половине половину половиной половин половинам половинами половинах половиною 

один одного одному одним одном 
первой первого первому первом первый первым первых 
во-первых 
единица единицы единице единицу единицей единиц единицам единицами единицах единицею 

два двумя двум двух двоих двое две 
второго второму второй втором вторым вторых 
двойка двойки двойке двойку двойкой двоек двойкам двойками двойках двойкою  
во-вторых 
оба обе обеим обеими обеих обоим обоими обоих 

полтора полторы полутора 

три третьего третьему третьем третьим третий тремя трем трех трое троих трёх 
тройка тройки тройке тройку тройкою троек тройкам тройками тройках тройкой 
троечка троечки троечке троечку троечкой троечек троечкам троечками троечках троечкой 
трешка трешки трешке трешку трешкой трешек трешкам трешками трешках трешкою 
трёшка трёшки трёшке трёшку трёшкой трёшек трёшкам трёшками трёшках трёшкою 
трояк трояка трояку трояком трояке трояки трояков троякам трояками трояках  
треха треху трехой 
трёха трёху трёхой 
втроем втроём 

четыре четвертого четвертому четвертом четвертый четвертым четверка четырьмя четырем четырех четверо четырёх четверым 
четверых 
вчетвером 

пять пятого пятому пятом пятый пятым пятью пяти пятеро пятерых пятерыми 
впятером 
пятерочка пятерочки пятерочке пятерочками пятерочкой пятерочку пятерочкой пятерочками 
пятёрочка пятёрочки пятёрочке пятёрочками пятёрочкой пятёрочку пятёрочкой пятёрочками 
пятерка пятерки пятерке пятерками пятеркой пятерку пятерками 
пятёрка пятёрки пятёрке пятёрками пятёркой пятёрку пятёрками 
пятёра пятёры пятёре пятёрами пятёрой пятёру пятёрами 
пятера пятеры пятере пятерами пятерой пятеру пятерами 
пятак пятаки пятаке пятаками пятаком пятаку пятаками 

шесть шестерка шестого шестому шестой шестом шестым шестью шести шестеро шестерых 
вшестером 

семь семерка седьмого седьмому седьмой седьмом седьмым семью семи семеро седьмых 
всемером 

восемь восьмерка восьмого восьмому восемью восьмой восьмом восьмым восеми восьмером восьми восьмью 
восьмерых 
ввосьмером 

девять девятого девятому девятка девятом девятый девятым девятью девяти девятером вдевятером девятерых 
вдевятером 

десять десятого десятому десятка десятом десятый десятым десятью десяти десятером десятых 
вдесятером 

одиннадцать одиннадцатого одиннадцатому одиннадцатом одиннадцатый одиннадцатым одиннадцатью одиннадцати 
одиннадцатых 

двенадцать двенадцатого двенадцатому двенадцатом двенадцатый двенадцатым двенадцатью двенадцати 
двенадцатых 

тринадцать тринадцатого тринадцатому тринадцатом тринадцатый тринадцатым тринадцатью тринадцати 
тринадцатых 

четырнадцать четырнадцатого четырнадцатому четырнадцатом четырнадцатый четырнадцатым четырнадцатью четырнадцати 
четырнадцатых 

пятнадцать пятнадцатого пятнадцатому пятнадцатом пятнадцатый пятнадцатым пятнадцатью пятнадцати 
пятнадцатых 
пятнарик пятнарику пятнариком пятнарики 

шестнадцать шестнадцатого шестнадцатому шестнадцатом шестнадцатый шестнадцатым шестнадцатью шестнадцати 
шестнадцатых 

семнадцать семнадцатого семнадцатому семнадцатом семнадцатый семнадцатым семнадцатью семнадцати семнадцатых 

восемнадцать восемнадцатого восемнадцатому восемнадцатом восемнадцатый восемнадцатым восемнадцатью восемнадцати 
восемнадцатых 

девятнадцать девятнадцатого девятнадцатому девятнадцатом девятнадцатый девятнадцатым девятнадцатью девятнадцати 
девятнадцатых 

двадцать двадцатого двадцатому двадцатом двадцатый двадцатым двадцатью двадцати двадцатых 

четвертак четвертака четвертаке четвертаку четвертаки четвертаком четвертаками 

тридцать тридцатого тридцатому тридцатом тридцатый тридцатым тридцатью тридцати тридцатых 
тридцадка тридцадку тридцадке тридцадки тридцадкой тридцадкою тридцадками 

тридевять тридевяти тридевятью 

сорок сорокового сороковому сороковом сороковым сороковой сороковых 
сорокет сорокета сорокету сорокете сорокеты сорокетом сорокетами сорокетам 

пятьдесят пятьдесятого пятьдесятому пятьюдесятью пятьдесятом пятьдесятый пятьдесятым пятидесяти пятьдесятых 
полтинник полтинника полтиннике полтиннику полтинники полтинником полтинниками полтинникам полтинниках 
пятидесятка пятидесятке пятидесятку пятидесятки пятидесяткой пятидесятками пятидесяткам пятидесятках 
полтос полтоса полтосе полтосу полтосы полтосом полтосами полтосам полтосах 

шестьдесят шестьдесятого шестьдесятому шестьюдесятью шестьдесятом шестьдесятый шестьдесятым шестидесятые шестидесяти 
шестьдесятых 

семьдесят семьдесятого семьдесятому семьюдесятью семьдесятом семьдесятый семьдесятым семидесяти семьдесятых 

восемьдесят восемьдесятого восемьдесятому восемьюдесятью восемьдесятом восемьдесятый восемьдесятым восемидесяти 
восьмидесяти восьмидесятых 

девяносто девяностого девяностому девяностом девяностый девяностым девяноста девяностых 

сто сотого сотому сотом сотен сотый сотым ста 
стольник стольника стольнику стольнике стольники стольником стольниками 
сотка сотки сотке соткой сотками соткам сотках 
сотня сотни сотне сотней сотнями сотням сотнях 

двести двумястами двухсотого двухсотому двухсотом двухсотый двухсотым двумстам двухстах двухсот 

триста тремястами трехсотого трехсотому трехсотом трехсотый трехсотым тремстам трехстах трехсот 

четыреста четырехсотого четырехсотому четырьмястами четырехсотом четырехсотый четырехсотым четыремстам четырехстах 
четырехсот 

пятьсот пятисотого пятисотому пятьюстами пятисотом пятисотый пятисотым пятистам пятистах пятисот 
пятисотка пятисотки пятисотке пятисоткой пятисотками пятисоткам пятисоткою пятисотках 
пятихатка пятихатки пятихатке пятихаткой пятихатками пятихаткам пятихаткою пятихатках 
пятифан пятифаны пятифане пятифаном пятифанами пятифанах 

шестьсот шестисотого шестисотому шестьюстами шестисотом шестисотый шестисотым шестистам шестистах шестисот 

семьсот семисотого семисотому семьюстами семисотом семисотый семисотым семистам семистах семисот 

восемьсот восемисотого восемисотому восемисотом восемисотый восемисотым восьмистами восьмистам восьмистах восьмисот 

девятьсот девятисотого девятисотому девятьюстами девятисотом девятисотый девятисотым девятистам девятистах девятисот 

тысяча тысячного тысячному тысячном тысячный тысячным тысячам тысячах тысячей тысяч тысячи тыс 
косарь косаря косару косарем косарями косарях косарям косарей 

десятитысячный десятитысячного десятитысячному десятитысячным десятитысячном десятитысячная десятитысячной 
десятитысячную десятитысячною десятитысячное десятитысячные десятитысячных десятитысячными 

двадцатитысячный двадцатитысячного двадцатитысячному двадцатитысячным двадцатитысячном двадцатитысячная 
двадцатитысячной двадцатитысячную двадцатитысячною двадцатитысячное двадцатитысячные двадцатитысячных 
двадцатитысячными 

тридцатитысячный тридцатитысячного тридцатитысячному тридцатитысячным тридцатитысячном тридцатитысячная 
тридцатитысячной тридцатитысячную тридцатитысячною тридцатитысячное тридцатитысячные тридцатитысячных 
тридцатитысячными 

сорокатысячный сорокатысячного сорокатысячному сорокатысячным сорокатысячном сорокатысячная 
сорокатысячной сорокатысячную сорокатысячною сорокатысячное сорокатысячные сорокатысячных 
сорокатысячными 

пятидесятитысячный пятидесятитысячного пятидесятитысячному пятидесятитысячным пятидесятитысячном пятидесятитысячная 
пятидесятитысячной пятидесятитысячную пятидесятитысячною пятидесятитысячное пятидесятитысячные пятидесятитысячных 
пятидесятитысячными 

шестидесятитысячный шестидесятитысячного шестидесятитысячному шестидесятитысячным шестидесятитысячном шестидесятитысячная 
шестидесятитысячной шестидесятитысячную шестидесятитысячною шестидесятитысячное шестидесятитысячные шестидесятитысячных 
шестидесятитысячными 

семидесятитысячный семидесятитысячного семидесятитысячному семидесятитысячным семидесятитысячном семидесятитысячная 
семидесятитысячной семидесятитысячную семидесятитысячною семидесятитысячное семидесятитысячные семидесятитысячных 
семидесятитысячными 

восьмидесятитысячный восьмидесятитысячного восьмидесятитысячному восьмидесятитысячным восьмидесятитысячном восьмидесятитысячная 
восьмидесятитысячной восьмидесятитысячную восьмидесятитысячною восьмидесятитысячное восьмидесятитысячные восьмидесятитысячных 
восьмидесятитысячными 

стотысячный стотысячного стотысячному стотысячным стотысячном стотысячная стотысячной стотысячную стотысячное 
стотысячные стотысячных стотысячными стотысячною 

миллион миллионного миллионов миллионному миллионном миллионный миллионным миллионом миллиона миллионе миллиону 
миллионов 
лям ляма лямы лямом лямами лямах лямов 
млн 

десятимиллионная десятимиллионной десятимиллионными десятимиллионный десятимиллионным десятимиллионному 
десятимиллионными десятимиллионную десятимиллионное  десятимиллионные десятимиллионных десятимиллионною 

миллиард миллиардного миллиардному миллиардном миллиардный миллиардным миллиардом миллиарда миллиарде миллиарду 
миллиардов 
лярд лярда лярды лярдом лярдами лярдах лярдов 
млрд 

триллион триллионного триллионному триллионном триллионный триллионным триллионом триллиона триллионе триллиону 
триллионов трлн 

квадриллион квадриллионного квадриллионному квадриллионный квадриллионным квадриллионом квадриллиона квадриллионе 
квадриллиону квадриллионов квадрлн 

квинтиллион квинтиллионного квинтиллионному квинтиллионный квинтиллионным квинтиллионом квинтиллиона квинтиллионе 
квинтиллиону квинтиллионов квинтлн 

i ii iii iv v vi vii viii ix x xi xii xiii xiv xv xvi xvii xviii xix xx xxi xxii xxiii xxiv xxv xxvi xxvii xxvii xxix
""".split()
    )
)


def like_num(text):
    if text.startswith(("+", "-", "±", "~")):
        text = text[1:]
    if text.endswith("%"):
        text = text[:-1]
    text = text.replace(",", "").replace(".", "")
    if text.isdigit():
        return True
    if text.count("/") == 1:
        num, denom = text.split("/")
        if num.isdigit() and denom.isdigit():
            return True
    if text.lower() in _num_words:
        return True
    return False


LEX_ATTRS = {LIKE_NUM: like_num}
