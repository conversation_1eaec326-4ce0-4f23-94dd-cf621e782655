from ...attrs import LIKE_NUM

# Thirteen, fifteen etc. are written separate: on üç

_num_words = [
    "bir",
    "iki",
    "üç",
    "dört",
    "beş",
    "altı",
    "yedi",
    "sekiz",
    "dokuz",
    "on",
    "yirmi",
    "otuz",
    "kırk",
    "elli",
    "altmış",
    "yetmi<PERSON>",
    "seksen",
    "doksan",
    "yüz",
    "bin",
    "milyon",
    "milyar",
    "trilyon",
    "katrilyon",
    "kentilyon",
]


_ordinal_words = [
    "birinci",
    "ikinci",
    "üçünc<PERSON>",
    "dörd<PERSON><PERSON><PERSON>",
    "beşinci",
    "altıncı",
    "yedinci",
    "sekizinci",
    "dokuzuncu",
    "onuncu",
    "yirminci",
    "otuzuncu",
    "kırkıncı",
    "ellinci",
    "altmışınc<PERSON>",
    "yetmi<PERSON>in<PERSON>",
    "seks<PERSON><PERSON><PERSON>",
    "doks<PERSON><PERSON><PERSON><PERSON>",
    "yüz<PERSON><PERSON><PERSON>",
    "bininci",
    "mily<PERSON><PERSON><PERSON>",
    "milya<PERSON><PERSON><PERSON><PERSON>",
    "trily<PERSON><PERSON><PERSON>",
    "katri<PERSON><PERSON><PERSON><PERSON>",
    "kentily<PERSON><PERSON><PERSON>",
]

_ordinal_endings = ("inci", "ıncı", "nci", "ncı", "uncu", "üncü")


def like_num(text):
    if text.startswith(("+", "-", "±", "~")):
        text = text[1:]
    text = text.replace(",", "").replace(".", "")
    if text.isdigit():
        return True
    if text.count("/") == 1:
        num, denom = text.split("/")
        if num.isdigit() and denom.isdigit():
            return True
    text_lower = text.lower()
    # Check cardinal number
    if text_lower in _num_words:
        return True
    # Check ordinal number
    if text_lower in _ordinal_words:
        return True
    if text_lower.endswith(_ordinal_endings):
        if text_lower[:-3].isdigit() or text_lower[:-4].isdigit():
            return True
    return False


LEX_ATTRS = {LIKE_NUM: like_num}
