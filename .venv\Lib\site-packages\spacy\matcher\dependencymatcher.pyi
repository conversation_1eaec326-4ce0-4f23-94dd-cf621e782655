from typing import Any, Callable, Dict, List, Optional, Tuple, Union

from ..tokens.doc import Doc
from ..tokens.span import Span
from ..vocab import Vocab
from .matcher import Matcher

class DependencyMatcher:
    """Match dependency parse tree based on pattern rules."""

    _patterns: Dict[str, List[Any]]
    _raw_patterns: Dict[str, List[Any]]
    _tokens_to_key: Dict[str, List[Any]]
    _root: Dict[str, List[Any]]
    _tree: Dict[str, List[Any]]
    _callbacks: Dict[
        Any, Callable[[DependencyMatcher, Doc, int, List[Tuple[int, List[int]]]], Any]
    ]
    _ops: Dict[str, Any]
    vocab: Vocab
    _matcher: Matcher
    def __init__(self, vocab: Vocab, *, validate: bool = ...) -> None: ...
    def __reduce__(
        self,
    ) -> Tuple[
        Callable[
            [Vocab, Dict[str, Any], Dict[str, Callable[..., Any]]], DependencyMatcher
        ],
        <PERSON><PERSON>[
            Vocab,
            Dict[str, List[Any]],
            Dict[
                str,
                Callable[
                    [DependencyMatcher, Doc, int, List[Tuple[int, List[int]]]], Any
                ],
            ],
        ],
        None,
        None,
    ]: ...
    def __len__(self) -> int: ...
    def __contains__(self, key: Union[str, int]) -> bool: ...
    def add(
        self,
        key: Union[str, int],
        patterns: List[List[Dict[str, Any]]],
        *,
        on_match: Optional[
            Callable[[DependencyMatcher, Doc, int, List[Tuple[int, List[int]]]], Any]
        ] = ...
    ) -> None: ...
    def has_key(self, key: Union[str, int]) -> bool: ...
    def get(
        self, key: Union[str, int], default: Optional[Any] = ...
    ) -> Tuple[
        Optional[
            Callable[[DependencyMatcher, Doc, int, List[Tuple[int, List[int]]]], Any]
        ],
        List[List[Dict[str, Any]]],
    ]: ...
    def remove(self, key: Union[str, int]) -> None: ...
    def __call__(self, doclike: Union[Doc, Span]) -> List[Tuple[int, List[int]]]: ...

def unpickle_matcher(
    vocab: Vocab, patterns: Dict[str, Any], callbacks: Dict[str, Callable[..., Any]]
) -> DependencyMatcher: ...
