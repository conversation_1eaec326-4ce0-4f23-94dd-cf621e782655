Metadata-Version: 2.1
Name: tensorflow
Version: 2.15.0
Summary: TensorFlow is an open source machine learning framework for everyone.
Home-page: https://www.tensorflow.org/
Author: Google Inc.
Author-email: <EMAIL>
License: Apache 2.0
Download-URL: https://github.com/tensorflow/tensorflow/tags
Keywords: tensorflow tensor machine learning
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: GPU :: NVIDIA CUDA :: 11.8
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.9
Classifier: Topic :: Scientific/Engineering
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Classifier: Topic :: Scientific/Engineering :: Mathematics
Classifier: Topic :: Software Development
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.9
Description-Content-Type: text/markdown
Requires-Dist: tensorflow-macos ==2.15.0 ; platform_system == "Darwin" and platform_machine == "arm64"
Requires-Dist: tensorflow-cpu-aws ==2.15.0 ; platform_system == "Linux" and (platform_machine == "arm64" or platform_machine == "aarch64")
Requires-Dist: tensorflow-intel ==2.15.0 ; platform_system == "Windows"
Provides-Extra: and-cuda
Requires-Dist: nvidia-cublas-cu12 ==******** ; extra == 'and-cuda'
Requires-Dist: nvidia-cuda-cupti-cu12 ==12.2.142 ; extra == 'and-cuda'
Requires-Dist: nvidia-cuda-nvcc-cu12 ==12.2.140 ; extra == 'and-cuda'
Requires-Dist: nvidia-cuda-nvrtc-cu12 ==12.2.140 ; extra == 'and-cuda'
Requires-Dist: nvidia-cuda-runtime-cu12 ==12.2.140 ; extra == 'and-cuda'
Requires-Dist: nvidia-cudnn-cu12 ==******** ; extra == 'and-cuda'
Requires-Dist: nvidia-cufft-cu12 ==********** ; extra == 'and-cuda'
Requires-Dist: nvidia-curand-cu12 ==********** ; extra == 'and-cuda'
Requires-Dist: nvidia-cusolver-cu12 ==********** ; extra == 'and-cuda'
Requires-Dist: nvidia-cusparse-cu12 ==12.1.2.141 ; extra == 'and-cuda'
Requires-Dist: nvidia-nccl-cu12 ==2.16.5 ; extra == 'and-cuda'
Requires-Dist: nvidia-nvjitlink-cu12 ==12.2.140 ; extra == 'and-cuda'
Requires-Dist: tensorrt-bindings ==8.6.1 ; extra == 'and-cuda'
Requires-Dist: tensorrt-libs ==8.6.1 ; extra == 'and-cuda'
Requires-Dist: tensorrt ==8.6.1.post1 ; extra == 'and-cuda'

[![Python](https://img.shields.io/pypi/pyversions/tensorflow.svg?style=plastic)](https://badge.fury.io/py/tensorflow)
[![PyPI](https://badge.fury.io/py/tensorflow.svg)](https://badge.fury.io/py/tensorflow)

TensorFlow is an open source software library for high performance numerical
computation. Its flexible architecture allows easy deployment of computation
across a variety of platforms (CPUs, GPUs, TPUs), and from desktops to clusters
of servers to mobile and edge devices.

Originally developed by researchers and engineers from the Google Brain team
within Google's AI organization, it comes with strong support for machine
learning and deep learning and the flexible numerical computation core is used
across many other scientific domains. TensorFlow is licensed under [Apache
2.0](https://github.com/tensorflow/tensorflow/blob/master/LICENSE).


