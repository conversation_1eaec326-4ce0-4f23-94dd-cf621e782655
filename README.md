﻿# AI Budget App 

An intelligent budgeting application that uses AI to analyze your financial data, categorize transactions, and provide personalized insights for better financial management.

##  Features

###  **Real-Time Financial Dashboard**
- Live account balance tracking
- Monthly spending analysis
- Budget utilization monitoring
- Category-wise expense breakdown

###  **AI-Powered Intelligence**
- **Smart Transaction Categorization**: Automatically categorizes transactions using NLP
- **Spending Pattern Analysis**: Identifies trends and unusual spending behavior
- **Predictive Insights**: Forecasts future spending based on historical data
- **Personalized Recommendations**: Suggests budget optimizations and savings opportunities

###  **Advanced Analytics**
- Category-wise spending breakdown
- Monthly spending trends
- Budget vs. actual spending comparison
- Anomaly detection for unusual transactions

###  **Data Integration**
- CSV bank data import and processing
- Real-time transaction processing
- Support for multiple account types
- Plaid API integration (ready for implementation)

###  **Modern Web Interface**
- Responsive design for all devices
- Interactive dashboard with real-time updates
- Beautiful data visualizations
- Intuitive user experience

##  Quick Start

### Prerequisites
- Python 3.8+
- Virtual environment (recommended)
- Modern web browser

### Installation

1. **Clone the repository**
   `ash
   git clone https://github.com/Mobius457/AI_Budget_App.git
   cd AI_Budget_App
   `

2. **Set up Python virtual environment**
   `ash
   python -m venv .venv
   
   # On Windows
   .venv\Scripts\activate
   
   # On macOS/Linux
   source .venv/bin/activate
   `

3. **Install dependencies**
   `ash
   pip install -r requirements.txt
   `

4. **Prepare your data**
   - Place your bank CSV file as your_bank_data.csv in the root directory
   - Ensure CSV has columns: Date, Description, Memo, Debit, Credit, Balance

5. **Start the backend server**
   `ash
   cd backend
   python main.py
   `
   Backend will be available at: http://localhost:8000

6. **Start the frontend server**
   `ash
   cd frontend
   python server.py
   `
   Frontend will be available at: http://localhost:3000

7. **Open your browser**
   Navigate to http://localhost:3000 to access the application

## ⚡ **Quick Start (Recommended)**

### **Option 1: One-Click Windows Launcher**
Simply double-click `start_app.bat` - it will:
- Create virtual environment (if needed)
- Install dependencies
- Start both servers
- Open your browser automatically

### **Option 2: Python Launcher**
```bash
python start_app.py
```
This single command will:
- Check requirements
- Install dependencies
- Start backend and frontend simultaneously
- Open browser automatically
- Monitor both servers

### **Option 3: Manual Setup (Advanced)**
Follow the detailed installation steps above for manual control.

## 📁 Project Structure

```
AI_Budget_App/
├── backend/                 # FastAPI backend application
│   ├── main.py             # Main application file with API endpoints
│   └── requirements.txt    # Backend dependencies
├── frontend/               # Web frontend
│   ├── index.html         # Main HTML file
│   ├── app.js            # JavaScript application logic
│   └── server.py         # Simple HTTP server for frontend
├── your_bank_data.csv     # Your bank transaction data
├── requirements.txt       # Project dependencies
└── README.md             # This file
```

## 🔧 API Endpoints

### Core Endpoints
- `GET /` - Health check
- `GET /health` - Detailed health status
- `GET /stats` - Financial statistics and analytics

### Transaction Management
- `GET /transactions` - Retrieve all transactions
- `POST /transactions` - Create new transaction
- `GET /transactions/{id}` - Get specific transaction

### Budget Management
- `GET /budgets` - Retrieve all budgets
- `POST /budgets` - Create new budget

### AI Features
- `GET /ai/insights` - Get AI-powered spending insights
- `GET /ai/categorize/{transaction_id}` - Categorize specific transaction
- `GET /ai/forecast` - Get spending forecasts

### Plaid Integration (Ready)
- `POST /plaid/link` - Link bank account via Plaid
- `GET /plaid/accounts` - Get linked accounts

## 🤖 AI Capabilities

### Transaction Categorization
The app automatically categorizes transactions into:
- **Food & Dining**: Restaurants, fast food, coffee shops
- **Transportation**: Gas, fuel, parking, rideshare
- **Shopping**: Retail stores, online purchases
- **Utilities**: Electric, water, internet, phone
- **Healthcare**: Medical, dental, pharmacy
- **Entertainment**: Movies, streaming, gaming
- **Financial**: Credit cards, loans, transfers
- **Income**: Salary, freelance, investments

### Intelligent Insights
- Spending trend analysis
- Budget adherence monitoring
- Unusual transaction detection
- Personalized saving recommendations
- Monthly spending forecasts

## 📊 Data Format

### CSV Requirements
Your bank CSV should have these columns:
```csv
Date,Description,Memo,Debit,Credit,Balance
07-07-25,Electronic Withdrawal CAPITAL,ONE  - MOBILE PMT,-617.42,,1329.4
07-06-25,Deposit ck gas,,,41,2327.32
```

### Supported Date Formats
- `MM-DD-YY` (e.g., 07-07-25)
- `MM/DD/YYYY` (e.g., 07/07/2025)

## 🛠 Technology Stack

### Backend
- **FastAPI**: Modern, fast web framework for building APIs
- **Python**: Core programming language
- **Pandas**: Data processing and analysis
- **Pydantic**: Data validation and serialization
- **Uvicorn**: ASGI server for FastAPI

### Frontend
- **HTML5/CSS3**: Modern web standards
- **JavaScript (ES6+)**: Interactive functionality
- **Bootstrap 5**: Responsive UI framework
- **Font Awesome**: Icon library

### AI/ML (Ready for Enhancement)
- **Scikit-learn**: Machine learning capabilities
- **NumPy**: Numerical computing
- **Natural Language Processing**: Transaction categorization

## 🔮 Future Enhancements

### Planned Features
- [ ] Real Plaid API integration for live bank connections
- [ ] Advanced machine learning models for better categorization
- [ ] Goal setting and tracking
- [ ] Bill reminder system
- [ ] Investment tracking
- [ ] Multi-user support with authentication
- [ ] Mobile app development
- [ ] Advanced data visualizations
- [ ] Export capabilities (PDF reports)
- [ ] Integration with other financial services

### AI Improvements
- [ ] Deep learning models for transaction categorization
- [ ] Predictive analytics for cash flow forecasting
- [ ] Anomaly detection for fraud prevention
- [ ] Natural language queries for financial data
- [ ] Automated budget recommendations

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- FastAPI for the excellent web framework
- Bootstrap for the responsive UI components
- The open-source community for various libraries and tools

## 📞 Support

If you encounter any issues or have questions:
1. Check the [Issues](https://github.com/Mobius457/AI_Budget_App/issues) page
2. Create a new issue with detailed information
3. Contact the maintainer: [<EMAIL>](mailto:<EMAIL>)

---

**Built with ❤️ for better financial management**
#   A I _ B u d g e t _ A p p 
 
 