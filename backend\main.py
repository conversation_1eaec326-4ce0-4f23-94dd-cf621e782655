"""
AI Budget App - FastAPI Backend
Main application entry point
"""

from fastapi import Fast<PERSON><PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from typing import List, Optional
import uvicorn
import os
from datetime import datetime, timedelta
import logging
import pandas as pd
import re
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="AI Budget App API",
    description="An intelligent budgeting application with AI-powered insights",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

# Pydantic models
class Transaction(BaseModel):
    id: Optional[int] = None
    amount: float
    description: str
    category: str
    date: datetime
    account_id: str

class Budget(BaseModel):
    id: Optional[int] = None
    category: str
    amount: float
    period: str  # monthly, weekly, yearly
    start_date: datetime
    end_date: datetime

class User(BaseModel):
    id: Optional[int] = None
    email: str
    name: str
    created_at: Optional[datetime] = None

class PlaidLinkRequest(BaseModel):
    public_token: str
    account_id: str

# In-memory storage (replace with database in production)
transactions_db = []
budgets_db = []
users_db = []

# CSV Data Processing Functions
def load_csv_data():
    """Load and process the CSV bank data"""
    csv_path = Path("../your_bank_data.csv")
    if not csv_path.exists():
        logger.warning("CSV file not found, using empty data")
        return []

    try:
        # Read CSV with proper handling of different date formats
        df = pd.read_csv(csv_path)

        # Clean and process the data
        processed_transactions = []

        for _, row in df.iterrows():
            try:
                # Parse date - handle different formats
                date_str = str(row['Date'])
                if '/' in date_str:
                    # Format: MM/DD/YYYY
                    date_obj = datetime.strptime(date_str, '%m/%d/%Y')
                else:
                    # Format: MM-DD-YY
                    date_obj = datetime.strptime(date_str, '%m-%d-%y')

                # Determine amount (negative for debits, positive for credits)
                debit = pd.to_numeric(row['Debit'], errors='coerce') if pd.notna(row['Debit']) else 0
                credit = pd.to_numeric(row['Credit'], errors='coerce') if pd.notna(row['Credit']) else 0
                amount = credit - debit  # Credits are positive, debits are negative

                # Clean description
                description = str(row['Description']).strip()

                # Categorize transaction using AI-like logic
                category = categorize_transaction(description, amount)

                transaction = {
                    "id": len(processed_transactions) + 1,
                    "amount": round(amount, 2),
                    "description": description,
                    "category": category,
                    "date": date_obj.isoformat(),
                    "account_id": "bank_account_1",
                    "balance": pd.to_numeric(row['Balance'], errors='coerce') if pd.notna(row['Balance']) else 0
                }

                processed_transactions.append(transaction)

            except Exception as e:
                logger.warning(f"Error processing row: {e}")
                continue

        logger.info(f"Loaded {len(processed_transactions)} transactions from CSV")
        return processed_transactions

    except Exception as e:
        logger.error(f"Error loading CSV data: {e}")
        return []

def categorize_transaction(description, amount):
    """AI-powered transaction categorization"""
    description_lower = description.lower()

    # Food & Dining
    food_keywords = ['mcdonald', 'burger', 'pizza', 'taco', 'sonic', 'chick-fil-a', 'restaurant',
                     'doordash', 'food', 'dining', 'cafe', 'coffee', 'starbucks', 'subway',
                     'wendys', 'kfc', 'arbys', 'hardees', 'krispy', 'dunkin', 'bakery']

    # Transportation
    transport_keywords = ['gas', 'fuel', 'exxon', 'shell', 'bp', 'chevron', 'texaco', 'racetrac',
                         'circle k', 'uber', 'lyft', 'taxi', 'parking']

    # Shopping
    shopping_keywords = ['walmart', 'target', 'amazon', 'sams', 'costco', 'kroger', 'publix',
                        'dollar', 'five below', 'old navy', 'nike', 'shopping']

    # Utilities
    utility_keywords = ['utilities', 'electric', 'water', 'gas bill', 'internet', 'phone',
                       'cable', 'madison co', 'huntsville util']

    # Healthcare
    healthcare_keywords = ['hospital', 'medical', 'doctor', 'pharmacy', 'dental', 'health',
                          'clinic', 'pediatric', 'dermatology']

    # Entertainment
    entertainment_keywords = ['netflix', 'spotify', 'gaming', 'movie', 'theater', 'entertainment',
                             'google play', 'app store', 'pokemon']

    # Financial/Credit
    financial_keywords = ['capital one', 'credit card', 'payment', 'barclaycard', 'venmo',
                         'paypal', 'transfer', 'mortgage', 'loan']

    # Income
    income_keywords = ['payroll', 'salary', 'deposit', 'torch technologi', 'commission',
                      'refund', 'cashout', 'rewards']

    # Check each category
    if any(keyword in description_lower for keyword in food_keywords):
        return "Food & Dining"
    elif any(keyword in description_lower for keyword in transport_keywords):
        return "Transportation"
    elif any(keyword in description_lower for keyword in shopping_keywords):
        return "Shopping"
    elif any(keyword in description_lower for keyword in utility_keywords):
        return "Utilities"
    elif any(keyword in description_lower for keyword in healthcare_keywords):
        return "Healthcare"
    elif any(keyword in description_lower for keyword in entertainment_keywords):
        return "Entertainment"
    elif any(keyword in description_lower for keyword in financial_keywords):
        return "Financial"
    elif any(keyword in description_lower for keyword in income_keywords) or amount > 0:
        return "Income"
    else:
        return "Other"

# Root endpoint
@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "AI Budget App API is running",
        "version": "1.0.0",
        "status": "healthy",
        "timestamp": datetime.now().isoformat()
    }

# Health check
@app.get("/health")
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "api": "running",
            "database": "connected",  # Will be updated when DB is connected
            "ml_models": "loaded"     # Will be updated when ML models are loaded
        }
    }

# Authentication endpoints
@app.post("/auth/register")
async def register_user(user: User):
    """Register a new user"""
    # Check if user already exists
    existing_user = next((u for u in users_db if u["email"] == user.email), None)
    if existing_user:
        raise HTTPException(status_code=400, detail="User already exists")
    
    # Create new user
    new_user = {
        "id": len(users_db) + 1,
        "email": user.email,
        "name": user.name,
        "created_at": datetime.now()
    }
    users_db.append(new_user)
    
    return {"message": "User registered successfully", "user_id": new_user["id"]}

@app.post("/auth/login")
async def login_user(email: str, password: str):
    """Login user (simplified for demo)"""
    user = next((u for u in users_db if u["email"] == email), None)
    if not user:
        raise HTTPException(status_code=401, detail="Invalid credentials")
    
    # In production, verify password hash
    return {
        "message": "Login successful",
        "user_id": user["id"],
        "access_token": "demo_token_" + str(user["id"])  # Replace with JWT
    }

# Transaction endpoints
@app.get("/transactions", response_model=List[Transaction])
async def get_transactions(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get all transactions for the authenticated user"""
    # Load CSV data if transactions_db is empty
    if not transactions_db:
        csv_transactions = load_csv_data()
        transactions_db.extend(csv_transactions)

    return transactions_db

@app.post("/transactions", response_model=Transaction)
async def create_transaction(transaction: Transaction, credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Create a new transaction"""
    new_transaction = transaction.dict()
    new_transaction["id"] = len(transactions_db) + 1
    transactions_db.append(new_transaction)
    
    logger.info(f"Created transaction: {new_transaction}")
    return new_transaction

@app.get("/transactions/{transaction_id}", response_model=Transaction)
async def get_transaction(transaction_id: int, credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get a specific transaction"""
    transaction = next((t for t in transactions_db if t["id"] == transaction_id), None)
    if not transaction:
        raise HTTPException(status_code=404, detail="Transaction not found")
    return transaction

# Budget endpoints
@app.get("/budgets", response_model=List[Budget])
async def get_budgets(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get all budgets for the authenticated user"""
    return budgets_db

@app.post("/budgets", response_model=Budget)
async def create_budget(budget: Budget, credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Create a new budget"""
    new_budget = budget.dict()
    new_budget["id"] = len(budgets_db) + 1
    budgets_db.append(new_budget)
    
    logger.info(f"Created budget: {new_budget}")
    return new_budget

# Plaid integration endpoints
@app.post("/plaid/link")
async def link_plaid_account(request: PlaidLinkRequest, credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Link a Plaid account"""
    # This is a placeholder - implement actual Plaid integration
    logger.info(f"Linking Plaid account: {request.account_id}")
    return {
        "message": "Account linked successfully",
        "account_id": request.account_id,
        "status": "connected"
    }

@app.get("/plaid/accounts")
async def get_plaid_accounts(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get linked Plaid accounts"""
    # Placeholder response
    return {
        "accounts": [
            {
                "account_id": "demo_account_1",
                "name": "Demo Checking Account",
                "type": "depository",
                "subtype": "checking",
                "balance": 2500.00
            }
        ]
    }

# AI/ML endpoints
@app.get("/ai/categorize/{transaction_id}")
async def categorize_transaction(transaction_id: int, credentials: HTTPAuthorizationCredentials = Depends(security)):
    """AI-powered transaction categorization"""
    transaction = next((t for t in transactions_db if t["id"] == transaction_id), None)
    if not transaction:
        raise HTTPException(status_code=404, detail="Transaction not found")
    
    # Placeholder AI categorization
    suggested_category = "Food & Dining"  # This would use ML model
    confidence = 0.85
    
    return {
        "transaction_id": transaction_id,
        "suggested_category": suggested_category,
        "confidence": confidence,
        "current_category": transaction.get("category")
    }

@app.get("/ai/insights")
async def get_spending_insights(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get AI-powered spending insights"""
    # Load CSV data if not already loaded
    if not transactions_db:
        csv_transactions = load_csv_data()
        transactions_db.extend(csv_transactions)

    # Analyze spending patterns
    insights = analyze_spending_patterns(transactions_db)
    recommendations = generate_recommendations(transactions_db)

    return {
        "insights": insights,
        "recommendations": recommendations
    }

def analyze_spending_patterns(transactions):
    """Analyze spending patterns from transaction data"""
    insights = []

    # Calculate spending by category
    category_spending = {}
    total_spending = 0

    # Get current month transactions
    current_month = datetime.now().month
    current_year = datetime.now().year

    for transaction in transactions:
        trans_date = datetime.fromisoformat(transaction['date'].replace('Z', '+00:00'))
        if trans_date.month == current_month and trans_date.year == current_year:
            category = transaction['category']
            amount = transaction['amount']

            if amount < 0:  # Only count expenses
                if category not in category_spending:
                    category_spending[category] = 0
                category_spending[category] += abs(amount)
                total_spending += abs(amount)

    # Find top spending categories
    sorted_categories = sorted(category_spending.items(), key=lambda x: x[1], reverse=True)

    if sorted_categories:
        top_category, top_amount = sorted_categories[0]
        insights.append({
            "type": "spending_trend",
            "message": f"Your highest spending category this month is {top_category} with ${top_amount:.2f}",
            "category": top_category,
            "impact": "high" if top_amount > 500 else "medium"
        })

    # Check for frequent small transactions
    small_transactions = [t for t in transactions if abs(t['amount']) < 20 and t['amount'] < 0]
    if len(small_transactions) > 10:
        insights.append({
            "type": "spending_pattern",
            "message": f"You have {len(small_transactions)} small transactions this month - these add up!",
            "category": "General",
            "impact": "medium"
        })

    # Check for large transactions
    large_transactions = [t for t in transactions if abs(t['amount']) > 200 and t['amount'] < 0]
    if large_transactions:
        insights.append({
            "type": "large_expense",
            "message": f"You had {len(large_transactions)} large expenses this month",
            "category": "General",
            "impact": "high"
        })

    return insights

def generate_recommendations(transactions):
    """Generate personalized recommendations"""
    recommendations = []

    # Calculate spending by category
    category_spending = {}
    for transaction in transactions:
        if transaction['amount'] < 0:  # Only expenses
            category = transaction['category']
            if category not in category_spending:
                category_spending[category] = 0
            category_spending[category] += abs(transaction['amount'])

    # Sort by spending amount
    sorted_categories = sorted(category_spending.items(), key=lambda x: x[1], reverse=True)

    if sorted_categories:
        top_category, top_amount = sorted_categories[0]

        if top_category == "Food & Dining" and top_amount > 300:
            recommendations.append("Consider meal planning and cooking at home to reduce dining expenses")

        if top_category == "Shopping" and top_amount > 400:
            recommendations.append("Try creating a shopping list and sticking to it to avoid impulse purchases")

        if top_category == "Transportation" and top_amount > 200:
            recommendations.append("Look into carpooling or public transportation to reduce fuel costs")

    # General recommendations
    recommendations.append("Set up automatic savings to build your emergency fund")
    recommendations.append("Review your subscriptions and cancel unused services")

    return recommendations

@app.get("/ai/forecast")
async def get_spending_forecast(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get spending forecast"""
    # Load CSV data if not already loaded
    if not transactions_db:
        csv_transactions = load_csv_data()
        transactions_db.extend(csv_transactions)

    forecast_data = generate_spending_forecast(transactions_db)
    return forecast_data

@app.get("/stats")
async def get_spending_stats(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get spending statistics"""
    # Load CSV data if not already loaded
    if not transactions_db:
        csv_transactions = load_csv_data()
        transactions_db.extend(csv_transactions)

    stats = calculate_spending_stats(transactions_db)
    return stats

def generate_spending_forecast(transactions):
    """Generate spending forecast based on historical data"""
    # Calculate average monthly spending by category
    monthly_spending = {}
    category_totals = {}

    for transaction in transactions:
        if transaction['amount'] < 0:  # Only expenses
            category = transaction['category']
            amount = abs(transaction['amount'])

            if category not in category_totals:
                category_totals[category] = 0
            category_totals[category] += amount

    # Estimate next month based on current patterns
    total_forecast = sum(category_totals.values()) / 3  # Rough 3-month average

    category_forecast = {}
    for category, total in category_totals.items():
        category_forecast[category] = round(total / 3, 2)

    return {
        "forecast": {
            "next_month_spending": round(total_forecast, 2),
            "confidence": 0.75,
            "categories": category_forecast
        },
        "trends": [
            "Based on your recent spending patterns",
            "Food & Dining is your largest expense category",
            "Consider setting budgets for top spending categories"
        ]
    }

def calculate_spending_stats(transactions):
    """Calculate comprehensive spending statistics"""
    total_income = 0
    total_expenses = 0
    category_spending = {}
    monthly_spending = {}

    current_balance = 0
    if transactions:
        current_balance = transactions[0].get('balance', 0)

    for transaction in transactions:
        amount = transaction['amount']
        category = transaction['category']
        trans_date = datetime.fromisoformat(transaction['date'].replace('Z', '+00:00'))
        month_key = f"{trans_date.year}-{trans_date.month:02d}"

        if amount > 0:
            total_income += amount
        else:
            total_expenses += abs(amount)

            # Category spending
            if category not in category_spending:
                category_spending[category] = 0
            category_spending[category] += abs(amount)

            # Monthly spending
            if month_key not in monthly_spending:
                monthly_spending[month_key] = 0
            monthly_spending[month_key] += abs(amount)

    # Get current month spending
    current_month = datetime.now()
    current_month_key = f"{current_month.year}-{current_month.month:02d}"
    current_month_spending = monthly_spending.get(current_month_key, 0)

    # Calculate average monthly spending (last 3 months)
    recent_months = sorted(monthly_spending.keys())[-3:]
    avg_monthly_spending = sum(monthly_spending[month] for month in recent_months) / len(recent_months) if recent_months else 0

    return {
        "total_balance": round(current_balance, 2),
        "monthly_spending": round(current_month_spending, 2),
        "total_income": round(total_income, 2),
        "total_expenses": round(total_expenses, 2),
        "avg_monthly_spending": round(avg_monthly_spending, 2),
        "category_breakdown": {k: round(v, 2) for k, v in sorted(category_spending.items(), key=lambda x: x[1], reverse=True)},
        "monthly_trend": {k: round(v, 2) for k, v in sorted(monthly_spending.items())[-6:]}  # Last 6 months
    }

if __name__ == "__main__":
    port = int(os.getenv("PORT", 8000))
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=port,
        reload=True,
        log_level="info"
    )
