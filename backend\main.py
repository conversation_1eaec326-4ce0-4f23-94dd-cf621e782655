"""
AI Budget App - FastAPI Backend
Main application entry point
"""

from fastapi import Fast<PERSON><PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from typing import List, Optional
import uvicorn
import os
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="AI Budget App API",
    description="An intelligent budgeting application with AI-powered insights",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

# Pydantic models
class Transaction(BaseModel):
    id: Optional[int] = None
    amount: float
    description: str
    category: str
    date: datetime
    account_id: str

class Budget(BaseModel):
    id: Optional[int] = None
    category: str
    amount: float
    period: str  # monthly, weekly, yearly
    start_date: datetime
    end_date: datetime

class User(BaseModel):
    id: Optional[int] = None
    email: str
    name: str
    created_at: Optional[datetime] = None

class PlaidLinkRequest(BaseModel):
    public_token: str
    account_id: str

# In-memory storage (replace with database in production)
transactions_db = []
budgets_db = []
users_db = []

# Root endpoint
@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "AI Budget App API is running",
        "version": "1.0.0",
        "status": "healthy",
        "timestamp": datetime.now().isoformat()
    }

# Health check
@app.get("/health")
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "api": "running",
            "database": "connected",  # Will be updated when DB is connected
            "ml_models": "loaded"     # Will be updated when ML models are loaded
        }
    }

# Authentication endpoints
@app.post("/auth/register")
async def register_user(user: User):
    """Register a new user"""
    # Check if user already exists
    existing_user = next((u for u in users_db if u["email"] == user.email), None)
    if existing_user:
        raise HTTPException(status_code=400, detail="User already exists")
    
    # Create new user
    new_user = {
        "id": len(users_db) + 1,
        "email": user.email,
        "name": user.name,
        "created_at": datetime.now()
    }
    users_db.append(new_user)
    
    return {"message": "User registered successfully", "user_id": new_user["id"]}

@app.post("/auth/login")
async def login_user(email: str, password: str):
    """Login user (simplified for demo)"""
    user = next((u for u in users_db if u["email"] == email), None)
    if not user:
        raise HTTPException(status_code=401, detail="Invalid credentials")
    
    # In production, verify password hash
    return {
        "message": "Login successful",
        "user_id": user["id"],
        "access_token": "demo_token_" + str(user["id"])  # Replace with JWT
    }

# Transaction endpoints
@app.get("/transactions", response_model=List[Transaction])
async def get_transactions(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get all transactions for the authenticated user"""
    return transactions_db

@app.post("/transactions", response_model=Transaction)
async def create_transaction(transaction: Transaction, credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Create a new transaction"""
    new_transaction = transaction.dict()
    new_transaction["id"] = len(transactions_db) + 1
    transactions_db.append(new_transaction)
    
    logger.info(f"Created transaction: {new_transaction}")
    return new_transaction

@app.get("/transactions/{transaction_id}", response_model=Transaction)
async def get_transaction(transaction_id: int, credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get a specific transaction"""
    transaction = next((t for t in transactions_db if t["id"] == transaction_id), None)
    if not transaction:
        raise HTTPException(status_code=404, detail="Transaction not found")
    return transaction

# Budget endpoints
@app.get("/budgets", response_model=List[Budget])
async def get_budgets(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get all budgets for the authenticated user"""
    return budgets_db

@app.post("/budgets", response_model=Budget)
async def create_budget(budget: Budget, credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Create a new budget"""
    new_budget = budget.dict()
    new_budget["id"] = len(budgets_db) + 1
    budgets_db.append(new_budget)
    
    logger.info(f"Created budget: {new_budget}")
    return new_budget

# Plaid integration endpoints
@app.post("/plaid/link")
async def link_plaid_account(request: PlaidLinkRequest, credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Link a Plaid account"""
    # This is a placeholder - implement actual Plaid integration
    logger.info(f"Linking Plaid account: {request.account_id}")
    return {
        "message": "Account linked successfully",
        "account_id": request.account_id,
        "status": "connected"
    }

@app.get("/plaid/accounts")
async def get_plaid_accounts(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get linked Plaid accounts"""
    # Placeholder response
    return {
        "accounts": [
            {
                "account_id": "demo_account_1",
                "name": "Demo Checking Account",
                "type": "depository",
                "subtype": "checking",
                "balance": 2500.00
            }
        ]
    }

# AI/ML endpoints
@app.get("/ai/categorize/{transaction_id}")
async def categorize_transaction(transaction_id: int, credentials: HTTPAuthorizationCredentials = Depends(security)):
    """AI-powered transaction categorization"""
    transaction = next((t for t in transactions_db if t["id"] == transaction_id), None)
    if not transaction:
        raise HTTPException(status_code=404, detail="Transaction not found")
    
    # Placeholder AI categorization
    suggested_category = "Food & Dining"  # This would use ML model
    confidence = 0.85
    
    return {
        "transaction_id": transaction_id,
        "suggested_category": suggested_category,
        "confidence": confidence,
        "current_category": transaction.get("category")
    }

@app.get("/ai/insights")
async def get_spending_insights(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get AI-powered spending insights"""
    # Placeholder insights
    return {
        "insights": [
            {
                "type": "spending_trend",
                "message": "Your dining expenses increased by 15% this month",
                "category": "Food & Dining",
                "impact": "medium"
            },
            {
                "type": "budget_alert",
                "message": "You're 80% through your entertainment budget",
                "category": "Entertainment",
                "impact": "high"
            }
        ],
        "recommendations": [
            "Consider setting a stricter dining budget",
            "Look for entertainment alternatives within budget"
        ]
    }

@app.get("/ai/forecast")
async def get_spending_forecast(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get spending forecast"""
    # Placeholder forecast
    return {
        "forecast": {
            "next_month_spending": 3200.00,
            "confidence": 0.78,
            "categories": {
                "Food & Dining": 800.00,
                "Transportation": 400.00,
                "Entertainment": 300.00,
                "Utilities": 200.00,
                "Other": 1500.00
            }
        },
        "trends": [
            "Spending likely to increase due to holiday season",
            "Transportation costs may decrease with remote work"
        ]
    }

if __name__ == "__main__":
    port = int(os.getenv("PORT", 8000))
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=port,
        reload=True,
        log_level="info"
    )
