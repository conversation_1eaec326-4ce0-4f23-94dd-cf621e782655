// AI Budget App Frontend JavaScript

const API_BASE_URL = 'http://localhost:8000';
let authToken = 'demo_token_1'; // In production, this would be managed properly

// Initialize the app
document.addEventListener('DOMContentLoaded', function() {
    checkApiConnection();
    loadDashboardData();
    
    // Set default date to today
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('date').value = today;
});

// API Helper Functions
async function apiCall(endpoint, method = 'GET', data = null) {
    const config = {
        method: method,
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`
        }
    };
    
    if (data) {
        config.body = JSON.stringify(data);
    }
    
    try {
        const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('API call failed:', error);
        throw error;
    }
}

// Check API Connection
async function checkApiConnection() {
    const statusElement = document.getElementById('apiStatus');
    
    try {
        const response = await fetch(`${API_BASE_URL}/health`);
        const data = await response.json();
        
        if (response.ok) {
            statusElement.className = 'alert alert-success';
            statusElement.innerHTML = '<i class="fas fa-check-circle me-2"></i>API Connected Successfully';
        } else {
            throw new Error('API health check failed');
        }
    } catch (error) {
        statusElement.className = 'alert alert-danger';
        statusElement.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>API Connection Failed - Using Demo Mode';
        console.error('API connection failed:', error);
    }
}

// Load Dashboard Data
async function loadDashboardData() {
    try {
        await Promise.all([
            loadTransactions(),
            loadBudgets(),
            loadAIInsights()
        ]);
        updateDashboardStats();
    } catch (error) {
        console.error('Failed to load dashboard data:', error);
        // Load demo data
        loadDemoData();
    }
}

// Load Transactions
async function loadTransactions() {
    try {
        const transactions = await apiCall('/transactions');
        displayTransactions(transactions);
        return transactions;
    } catch (error) {
        console.error('Failed to load transactions:', error);
        document.getElementById('transactionsList').innerHTML = 
            '<p class="text-muted">Failed to load transactions. Please try again.</p>';
        return [];
    }
}

// Display Transactions
function displayTransactions(transactions) {
    const container = document.getElementById('transactionsList');
    const recentContainer = document.getElementById('recentTransactions');
    
    if (transactions.length === 0) {
        container.innerHTML = '<p class="text-muted">No transactions found. Add your first transaction!</p>';
        recentContainer.innerHTML = '<p class="text-muted">No recent transactions.</p>';
        return;
    }
    
    const transactionHTML = transactions.map(transaction => `
        <div class="transaction-item">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">${transaction.description}</h6>
                    <small class="text-muted">${transaction.category} • ${new Date(transaction.date).toLocaleDateString()}</small>
                </div>
                <div class="text-end">
                    <span class="h5 ${transaction.amount < 0 ? 'text-danger' : 'text-success'}">
                        ${transaction.amount < 0 ? '-' : '+'}$${Math.abs(transaction.amount).toFixed(2)}
                    </span>
                </div>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = transactionHTML;
    
    // Show recent transactions (last 5)
    const recentTransactions = transactions.slice(-5);
    recentContainer.innerHTML = recentTransactions.map(transaction => `
        <div class="d-flex justify-content-between align-items-center mb-2">
            <div>
                <small class="fw-bold">${transaction.description}</small><br>
                <small class="text-muted">${transaction.category}</small>
            </div>
            <span class="badge ${transaction.amount < 0 ? 'bg-danger' : 'bg-success'}">
                ${transaction.amount < 0 ? '-' : '+'}$${Math.abs(transaction.amount).toFixed(2)}
            </span>
        </div>
    `).join('');
}

// Load Budgets
async function loadBudgets() {
    try {
        const budgets = await apiCall('/budgets');
        displayBudgets(budgets);
        return budgets;
    } catch (error) {
        console.error('Failed to load budgets:', error);
        document.getElementById('budgetsList').innerHTML = 
            '<p class="text-muted">Failed to load budgets. Please try again.</p>';
        return [];
    }
}

// Display Budgets
function displayBudgets(budgets) {
    const container = document.getElementById('budgetsList');
    const progressContainer = document.getElementById('budgetProgress');
    
    if (budgets.length === 0) {
        container.innerHTML = '<p class="text-muted">No budgets found. Create your first budget!</p>';
        progressContainer.innerHTML = '<p class="text-muted">No budgets set.</p>';
        return;
    }
    
    const budgetHTML = budgets.map(budget => {
        const progress = Math.min((budget.spent || 0) / budget.amount * 100, 100);
        const progressClass = progress > 80 ? 'bg-danger' : progress > 60 ? 'bg-warning' : 'bg-success';
        
        return `
            <div class="card mb-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="card-title mb-0">${budget.category}</h6>
                        <span class="badge bg-primary">${budget.period}</span>
                    </div>
                    <div class="progress mb-2" style="height: 10px;">
                        <div class="progress-bar ${progressClass}" style="width: ${progress}%"></div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <small class="text-muted">$${(budget.spent || 0).toFixed(2)} spent</small>
                        <small class="text-muted">$${budget.amount.toFixed(2)} budget</small>
                    </div>
                </div>
            </div>
        `;
    }).join('');
    
    container.innerHTML = budgetHTML;
    
    // Show budget progress summary
    progressContainer.innerHTML = budgets.slice(0, 3).map(budget => {
        const progress = Math.min((budget.spent || 0) / budget.amount * 100, 100);
        return `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <small class="fw-bold">${budget.category}</small>
                <span class="badge ${progress > 80 ? 'bg-danger' : progress > 60 ? 'bg-warning' : 'bg-success'}">
                    ${progress.toFixed(0)}%
                </span>
            </div>
        `;
    }).join('');
}

// Load AI Insights
async function loadAIInsights() {
    try {
        const insights = await apiCall('/ai/insights');
        displayAIInsights(insights);
    } catch (error) {
        console.error('Failed to load AI insights:', error);
        document.getElementById('aiInsights').innerHTML = 
            '<p class="text-muted">Failed to load AI insights. Please try again.</p>';
    }
}

// Display AI Insights
function displayAIInsights(data) {
    const container = document.getElementById('aiInsights');
    
    const insightsHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-lightbulb me-2"></i>Insights</h6>
                ${data.insights.map(insight => `
                    <div class="alert alert-${insight.impact === 'high' ? 'warning' : 'info'} mb-2">
                        <small><strong>${insight.type.replace('_', ' ').toUpperCase()}:</strong> ${insight.message}</small>
                    </div>
                `).join('')}
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-recommendations me-2"></i>Recommendations</h6>
                ${data.recommendations.map(rec => `
                    <div class="alert alert-success mb-2">
                        <small><i class="fas fa-check me-2"></i>${rec}</small>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
    
    container.innerHTML = insightsHTML;
}

// Update Dashboard Stats
function updateDashboardStats() {
    // This would calculate stats from actual data
    // For demo purposes, showing placeholder values
    document.getElementById('totalBalance').textContent = '$2,500.00';
    document.getElementById('monthlySpending').textContent = '$1,200.00';
    document.getElementById('totalBudget').textContent = '$2,000.00';
    document.getElementById('budgetUsed').textContent = '60%';
}

// Show Add Transaction Modal
function showAddTransactionModal() {
    const modal = new bootstrap.Modal(document.getElementById('addTransactionModal'));
    modal.show();
}

// Add Transaction
async function addTransaction() {
    const form = document.getElementById('transactionForm');
    const formData = new FormData(form);
    
    const transaction = {
        amount: parseFloat(formData.get('amount')),
        description: formData.get('description'),
        category: formData.get('category'),
        date: formData.get('date') + 'T00:00:00',
        account_id: 'demo_account_1'
    };
    
    try {
        await apiCall('/transactions', 'POST', transaction);
        
        // Close modal and refresh data
        const modal = bootstrap.Modal.getInstance(document.getElementById('addTransactionModal'));
        modal.hide();
        form.reset();
        
        // Refresh transactions
        await loadTransactions();
        updateDashboardStats();
        
        // Show success message
        showAlert('Transaction added successfully!', 'success');
    } catch (error) {
        console.error('Failed to add transaction:', error);
        showAlert('Failed to add transaction. Please try again.', 'danger');
    }
}

// Show Alert
function showAlert(message, type) {
    const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Insert at the top of the main container
    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHTML);
    
    // Auto-dismiss after 3 seconds
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 3000);
}

// Load Demo Data (fallback when API is not available)
function loadDemoData() {
    const demoTransactions = [
        {
            id: 1,
            amount: -25.50,
            description: "Coffee Shop",
            category: "Food & Dining",
            date: "2025-07-08T10:30:00"
        },
        {
            id: 2,
            amount: -120.00,
            description: "Grocery Store",
            category: "Food & Dining",
            date: "2025-07-07T15:45:00"
        },
        {
            id: 3,
            amount: 2500.00,
            description: "Salary Deposit",
            category: "Income",
            date: "2025-07-01T09:00:00"
        }
    ];
    
    const demoBudgets = [
        {
            id: 1,
            category: "Food & Dining",
            amount: 500.00,
            spent: 145.50,
            period: "monthly"
        },
        {
            id: 2,
            category: "Transportation",
            amount: 200.00,
            spent: 80.00,
            period: "monthly"
        }
    ];
    
    const demoInsights = {
        insights: [
            {
                type: "spending_trend",
                message: "Your dining expenses increased by 15% this month",
                category: "Food & Dining",
                impact: "medium"
            }
        ],
        recommendations: [
            "Consider setting a stricter dining budget",
            "Look for entertainment alternatives within budget"
        ]
    };
    
    displayTransactions(demoTransactions);
    displayBudgets(demoBudgets);
    displayAIInsights(demoInsights);
    updateDashboardStats();
}
