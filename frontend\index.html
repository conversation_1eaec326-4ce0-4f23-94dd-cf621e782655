<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Budget App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 20px auto;
            max-width: 1200px;
        }
        
        .navbar-brand {
            font-weight: bold;
            color: #667eea !important;
        }
        
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
        }
        
        .btn-primary:hover {
            background: linear-gradient(45deg, #764ba2, #667eea);
        }
        
        .stat-card {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .transaction-item {
            border-left: 4px solid #667eea;
            padding: 15px;
            margin-bottom: 10px;
            background: #f8f9fa;
            border-radius: 0 10px 10px 0;
        }
        
        .loading {
            display: none;
        }
        
        .alert {
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light bg-light rounded-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-chart-line me-2"></i>AI Budget App
                </a>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text">
                        <i class="fas fa-user-circle me-2"></i>Welcome, User
                    </span>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="container-fluid p-4">
            <!-- API Status -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="alert alert-info" id="apiStatus">
                        <i class="fas fa-spinner fa-spin me-2"></i>Checking API connection...
                    </div>
                </div>
            </div>

            <!-- Dashboard Stats -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <i class="fas fa-wallet fa-2x mb-2"></i>
                        <h4 id="totalBalance">$0.00</h4>
                        <p class="mb-0">Total Balance</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <i class="fas fa-arrow-down fa-2x mb-2"></i>
                        <h4 id="monthlySpending">$0.00</h4>
                        <p class="mb-0">Monthly Spending</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <i class="fas fa-piggy-bank fa-2x mb-2"></i>
                        <h4 id="totalBudget">$0.00</h4>
                        <p class="mb-0">Total Budget</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <i class="fas fa-chart-pie fa-2x mb-2"></i>
                        <h4 id="budgetUsed">0%</h4>
                        <p class="mb-0">Budget Used</p>
                    </div>
                </div>
            </div>

            <!-- Main Content Tabs -->
            <div class="row">
                <div class="col-12">
                    <ul class="nav nav-tabs" id="mainTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="dashboard-tab" data-bs-toggle="tab" data-bs-target="#dashboard" type="button" role="tab">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="transactions-tab" data-bs-toggle="tab" data-bs-target="#transactions" type="button" role="tab">
                                <i class="fas fa-exchange-alt me-2"></i>Transactions
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="budgets-tab" data-bs-toggle="tab" data-bs-target="#budgets" type="button" role="tab">
                                <i class="fas fa-chart-bar me-2"></i>Budgets
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="insights-tab" data-bs-toggle="tab" data-bs-target="#insights" type="button" role="tab">
                                <i class="fas fa-brain me-2"></i>AI Insights
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content" id="mainTabsContent">
                        <!-- Dashboard Tab -->
                        <div class="tab-pane fade show active" id="dashboard" role="tabpanel">
                            <div class="card mt-3">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-chart-line me-2"></i>Financial Overview
                                    </h5>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <h6>Recent Transactions</h6>
                                            <div id="recentTransactions">
                                                <p class="text-muted">Loading transactions...</p>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <h6>Spending by Category</h6>
                                            <div id="categoryBreakdown">
                                                <p class="text-muted">Loading category data...</p>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <h6>Budget Progress</h6>
                                            <div id="budgetProgress">
                                                <p class="text-muted">No budgets set. Create budgets to track your spending.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Transactions Tab -->
                        <div class="tab-pane fade" id="transactions" role="tabpanel">
                            <div class="card mt-3">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="card-title mb-0">
                                            <i class="fas fa-exchange-alt me-2"></i>Transactions
                                        </h5>
                                        <button class="btn btn-primary" onclick="showAddTransactionModal()">
                                            <i class="fas fa-plus me-2"></i>Add Transaction
                                        </button>
                                    </div>
                                    <div id="transactionsList">
                                        <p class="text-muted">Loading transactions...</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Budgets Tab -->
                        <div class="tab-pane fade" id="budgets" role="tabpanel">
                            <div class="card mt-3">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="card-title mb-0">
                                            <i class="fas fa-chart-bar me-2"></i>Budgets
                                        </h5>
                                        <button class="btn btn-primary" onclick="showAddBudgetModal()">
                                            <i class="fas fa-plus me-2"></i>Create Budget
                                        </button>
                                    </div>
                                    <div id="budgetsList">
                                        <p class="text-muted">Loading budgets...</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- AI Insights Tab -->
                        <div class="tab-pane fade" id="insights" role="tabpanel">
                            <div class="card mt-3">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-brain me-2"></i>AI-Powered Insights
                                    </h5>
                                    <div id="aiInsights">
                                        <p class="text-muted">Loading AI insights...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Transaction Modal -->
    <div class="modal fade" id="addTransactionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add Transaction</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="transactionForm">
                        <div class="mb-3">
                            <label for="amount" class="form-label">Amount</label>
                            <input type="number" class="form-control" id="amount" step="0.01" required>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <input type="text" class="form-control" id="description" required>
                        </div>
                        <div class="mb-3">
                            <label for="category" class="form-label">Category</label>
                            <select class="form-control" id="category" required>
                                <option value="">Select Category</option>
                                <option value="Food & Dining">Food & Dining</option>
                                <option value="Transportation">Transportation</option>
                                <option value="Entertainment">Entertainment</option>
                                <option value="Utilities">Utilities</option>
                                <option value="Shopping">Shopping</option>
                                <option value="Healthcare">Healthcare</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="date" class="form-label">Date</label>
                            <input type="date" class="form-control" id="date" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="addTransaction()">Add Transaction</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
