@echo off
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🤖💰 AI Budget App 💰🤖                    ║
echo ║                                                              ║
echo ║              Quick Start Launcher (Windows)                 ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Check if virtual environment exists
if not exist ".venv\Scripts\activate.bat" (
    echo 🔧 Creating virtual environment...
    python -m venv .venv
    if errorlevel 1 (
        echo ❌ Failed to create virtual environment
        echo Please make sure Python is installed and in your PATH
        pause
        exit /b 1
    )
)

REM Activate virtual environment
echo 🔄 Activating virtual environment...
call .venv\Scripts\activate.bat

REM Install dependencies
echo 📦 Installing/updating dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

REM Start the application
echo 🚀 Starting AI Budget App...
python start_app.py

echo.
echo 👋 Thanks for using AI Budget App!
pause
