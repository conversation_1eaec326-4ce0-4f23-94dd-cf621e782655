#!/usr/bin/env python3
"""
AI Budget App Launcher
Starts both frontend and backend servers simultaneously
"""

import subprocess
import sys
import os
import time
import threading
import webbrowser
from pathlib import Path

def print_banner():
    """Print a nice banner for the app"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🤖💰 AI Budget App 💰🤖                    ║
    ║                                                              ║
    ║              Starting Frontend and Backend Servers          ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_requirements():
    """Check if all requirements are met"""
    print("🔍 Checking requirements...")
    
    # Check if we're in the right directory
    if not Path("your_bank_data.csv").exists():
        print("⚠️  Warning: your_bank_data.csv not found in current directory")
        print("   The app will work but won't have your real data")
    else:
        print("✅ CSV data file found")
    
    # Check if backend files exist
    if not Path("backend/main.py").exists():
        print("❌ Error: backend/main.py not found")
        return False
    
    # Check if frontend files exist
    if not Path("frontend/server.py").exists():
        print("❌ Error: frontend/server.py not found")
        return False
    
    print("✅ All required files found")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def start_backend():
    """Start the backend server"""
    print("🚀 Starting backend server...")
    try:
        # Change to backend directory and start the server
        backend_process = subprocess.Popen(
            [sys.executable, "main.py"],
            cwd="backend",
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait a moment to see if it starts successfully
        time.sleep(2)
        
        if backend_process.poll() is None:  # Process is still running
            print("✅ Backend server started successfully on http://localhost:8000")
            return backend_process
        else:
            stdout, stderr = backend_process.communicate()
            print(f"❌ Backend failed to start:")
            print(f"   stdout: {stdout}")
            print(f"   stderr: {stderr}")
            return None
            
    except Exception as e:
        print(f"❌ Error starting backend: {e}")
        return None

def start_frontend():
    """Start the frontend server"""
    print("🌐 Starting frontend server...")
    try:
        # Change to frontend directory and start the server
        frontend_process = subprocess.Popen(
            [sys.executable, "server.py"],
            cwd="frontend",
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait a moment to see if it starts successfully
        time.sleep(2)
        
        if frontend_process.poll() is None:  # Process is still running
            print("✅ Frontend server started successfully on http://localhost:3000")
            return frontend_process
        else:
            stdout, stderr = frontend_process.communicate()
            print(f"❌ Frontend failed to start:")
            print(f"   stdout: {stdout}")
            print(f"   stderr: {stderr}")
            return None
            
    except Exception as e:
        print(f"❌ Error starting frontend: {e}")
        return None

def open_browser():
    """Open the browser after a delay"""
    print("⏳ Waiting for servers to fully initialize...")
    time.sleep(5)  # Wait 5 seconds for servers to be ready
    print("🌐 Opening browser...")
    try:
        webbrowser.open("http://localhost:3000")
        print("✅ Browser opened successfully")
    except Exception as e:
        print(f"⚠️  Could not open browser automatically: {e}")
        print("   Please manually open: http://localhost:3000")

def monitor_processes(backend_process, frontend_process):
    """Monitor both processes and restart if needed"""
    print("\n📊 Monitoring servers... Press Ctrl+C to stop both servers")
    print("=" * 60)
    
    try:
        while True:
            # Check if processes are still running
            if backend_process and backend_process.poll() is not None:
                print("❌ Backend server stopped unexpectedly")
                break
                
            if frontend_process and frontend_process.poll() is not None:
                print("❌ Frontend server stopped unexpectedly")
                break
            
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Shutting down servers...")
        
        # Terminate processes
        if backend_process:
            backend_process.terminate()
            print("✅ Backend server stopped")
            
        if frontend_process:
            frontend_process.terminate()
            print("✅ Frontend server stopped")
        
        print("👋 AI Budget App stopped successfully")

def main():
    """Main function to start the application"""
    print_banner()
    
    # Check requirements
    if not check_requirements():
        print("❌ Requirements check failed. Please fix the issues above.")
        return
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies. Please check your Python installation.")
        return
    
    # Start backend server
    backend_process = start_backend()
    if not backend_process:
        print("❌ Failed to start backend server")
        return
    
    # Start frontend server
    frontend_process = start_frontend()
    if not frontend_process:
        print("❌ Failed to start frontend server")
        if backend_process:
            backend_process.terminate()
        return
    
    # Open browser in a separate thread
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # Show success message
    print("\n🎉 AI Budget App is now running!")
    print("📊 Backend API: http://localhost:8000")
    print("🌐 Frontend App: http://localhost:3000")
    print("📖 API Docs: http://localhost:8000/docs")
    
    # Monitor processes
    monitor_processes(backend_process, frontend_process)

if __name__ == "__main__":
    main()
